from config.database import db_manager
from datetime import datetime
from models.product import Product

class InventoryTransaction:
    def __init__(self, id=None, product_id=None, transaction_type=None, quantity=0,
                 unit_price=0, total_amount=0, reference_number=None, supplier_id=None,
                 user_id=None, notes=None, transaction_date=None):
        self.id = id
        self.product_id = product_id
        self.transaction_type = transaction_type  # 'in', 'out', 'adjustment'
        self.quantity = quantity
        self.unit_price = unit_price
        self.total_amount = total_amount
        self.reference_number = reference_number
        self.supplier_id = supplier_id
        self.user_id = user_id
        self.notes = notes
        self.transaction_date = transaction_date or datetime.now()
    
    def save(self):
        """حفظ المعاملة وتحديث كمية المنتج"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # حفظ المعاملة
        if self.id:
            cursor.execute('''
                UPDATE inventory_transactions SET
                    product_id = ?, transaction_type = ?, quantity = ?, unit_price = ?,
                    total_amount = ?, reference_number = ?, supplier_id = ?, user_id = ?,
                    notes = ?, transaction_date = ?
                WHERE id = ?
            ''', (self.product_id, self.transaction_type, self.quantity, self.unit_price,
                  self.total_amount, self.reference_number, self.supplier_id, self.user_id,
                  self.notes, self.transaction_date, self.id))
        else:
            cursor.execute('''
                INSERT INTO inventory_transactions (
                    product_id, transaction_type, quantity, unit_price, total_amount,
                    reference_number, supplier_id, user_id, notes, transaction_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.product_id, self.transaction_type, self.quantity, self.unit_price,
                  self.total_amount, self.reference_number, self.supplier_id, self.user_id,
                  self.notes, self.transaction_date))
            
            self.id = cursor.lastrowid
        
        # تحديث كمية المنتج
        product = Product.get_by_id(self.product_id)
        if product:
            if self.transaction_type == 'in':
                new_quantity = product.current_quantity + self.quantity
            elif self.transaction_type == 'out':
                new_quantity = product.current_quantity - self.quantity
            else:  # adjustment
                new_quantity = self.quantity
            
            product.update_quantity(new_quantity)
        
        conn.commit()
        conn.close()
        return self.id
    
    @staticmethod
    def get_by_id(transaction_id):
        """الحصول على معاملة بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM inventory_transactions WHERE id = ?', (transaction_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
        return None
    
    @staticmethod
    def get_all(limit=None):
        """الحصول على جميع المعاملات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY t.transaction_date DESC
        '''
        
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query)
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions
    
    @staticmethod
    def get_by_product(product_id, limit=None):
        """الحصول على معاملات منتج معين"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE t.product_id = ?
            ORDER BY t.transaction_date DESC
        '''
        
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query, (product_id,))
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions
    
    @staticmethod
    def get_by_date_range(start_date, end_date):
        """الحصول على معاملات في فترة زمنية"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE DATE(t.transaction_date) BETWEEN ? AND ?
            ORDER BY t.transaction_date DESC
        ''', (start_date, end_date))
        
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions

class PurchaseOrder:
    def __init__(self, id=None, order_number=None, supplier_id=None, order_date=None,
                 expected_date=None, status='pending', total_amount=0, notes=None, user_id=None):
        self.id = id
        self.order_number = order_number
        self.supplier_id = supplier_id
        self.order_date = order_date or datetime.now()
        self.expected_date = expected_date
        self.status = status  # 'pending', 'received', 'cancelled'
        self.total_amount = total_amount
        self.notes = notes
        self.user_id = user_id
        self.items = []
    
    def save(self):
        """حفظ أمر التوريد"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if self.id:
            cursor.execute('''
                UPDATE purchase_orders SET
                    order_number = ?, supplier_id = ?, order_date = ?, expected_date = ?,
                    status = ?, total_amount = ?, notes = ?, user_id = ?
                WHERE id = ?
            ''', (self.order_number, self.supplier_id, self.order_date, self.expected_date,
                  self.status, self.total_amount, self.notes, self.user_id, self.id))
        else:
            cursor.execute('''
                INSERT INTO purchase_orders (
                    order_number, supplier_id, order_date, expected_date,
                    status, total_amount, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.order_number, self.supplier_id, self.order_date, self.expected_date,
                  self.status, self.total_amount, self.notes, self.user_id))
            
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    @staticmethod
    def get_all():
        """الحصول على جميع أوامر التوريد"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT po.*, s.name as supplier_name, u.full_name as user_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN users u ON po.user_id = u.id
            ORDER BY po.order_date DESC
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        orders = []
        for row in rows:
            order = PurchaseOrder(
                id=row['id'], order_number=row['order_number'],
                supplier_id=row['supplier_id'], order_date=row['order_date'],
                expected_date=row['expected_date'], status=row['status'],
                total_amount=row['total_amount'], notes=row['notes'],
                user_id=row['user_id']
            )
            order.supplier_name = row['supplier_name']
            order.user_name = row['user_name']
            orders.append(order)
        
        return orders
