import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os
from datetime import datetime

from config.settings import app_settings
from models.product import Product
from models.user import User
from models.transaction import InventoryTransaction

class MainWindow:
    def __init__(self):
        self.current_user = None
        self.setup_window()
        self.create_login_frame()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        ctk.set_appearance_mode(app_settings.get('theme', 'dark'))
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title("نظام إدارة المخازن")
        self.root.geometry(f"{app_settings.get('window_width')}x{app_settings.get('window_height')}")
        self.root.minsize(800, 600)
        
        # تعيين الخط العربي
        self.arabic_font = ("Arial", app_settings.get('font_size', 12))
        
        # إنشاء الإطار الرئيسي
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    def create_login_frame(self):
        """إنشاء واجهة تسجيل الدخول"""
        # مسح المحتوى الحالي
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(self.main_frame)
        login_frame.place(relx=0.5, rely=0.5, anchor="center")
        
        # عنوان
        title_label = ctk.CTkLabel(
            login_frame,
            text="نظام إدارة المخازن",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=20, padx=40)
        
        # حقل اسم المستخدم
        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="اسم المستخدم",
            font=self.arabic_font,
            width=250
        )
        self.username_entry.pack(pady=10, padx=40)
        
        # حقل كلمة المرور
        self.password_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="كلمة المرور",
            show="*",
            font=self.arabic_font,
            width=250
        )
        self.password_entry.pack(pady=10, padx=40)
        
        # زر تسجيل الدخول
        login_button = ctk.CTkButton(
            login_frame,
            text="تسجيل الدخول",
            command=self.login,
            font=self.arabic_font,
            width=250
        )
        login_button.pack(pady=20, padx=40)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
        # تعيين القيم الافتراضية للاختبار
        self.username_entry.insert(0, "admin")
        self.password_entry.insert(0, "admin123")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        user = User.authenticate(username, password)
        if user:
            self.current_user = user
            self.create_main_interface()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية بعد تسجيل الدخول"""
        # مسح المحتوى الحالي
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # إنشاء شريط القوائم العلوي
        self.create_top_bar()
        
        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء المنطقة الرئيسية
        self.create_content_area()
        
        # عرض لوحة المعلومات الافتراضية
        self.show_dashboard()
    
    def create_top_bar(self):
        """إنشاء الشريط العلوي"""
        self.top_bar = ctk.CTkFrame(self.main_frame, height=60)
        self.top_bar.pack(fill="x", padx=5, pady=(5, 0))
        self.top_bar.pack_propagate(False)
        
        # معلومات المستخدم
        user_frame = ctk.CTkFrame(self.top_bar)
        user_frame.pack(side="right", padx=10, pady=10)
        
        user_label = ctk.CTkLabel(
            user_frame,
            text=f"مرحباً، {self.current_user.full_name}",
            font=self.arabic_font
        )
        user_label.pack(side="right", padx=10)
        
        logout_button = ctk.CTkButton(
            user_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=30
        )
        logout_button.pack(side="right", padx=5)
        
        # عنوان النظام
        title_label = ctk.CTkLabel(
            self.top_bar,
            text="نظام إدارة المخازن",
            font=("Arial", 18, "bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar = ctk.CTkFrame(self.main_frame, width=200)
        self.sidebar.pack(side="right", fill="y", padx=(0, 5), pady=5)
        self.sidebar.pack_propagate(False)
        
        # قائمة الوحدات
        modules = [
            ("🏠", "لوحة المعلومات", self.show_dashboard),
            ("📦", "إدارة المنتجات", self.show_products),
            ("📊", "المخزون", self.show_inventory),
            ("🚚", "الواردات", self.show_incoming),
            ("📤", "الصادرات", self.show_outgoing),
            ("🔄", "حركات المخزون", self.show_transactions),
            ("🏢", "الموردين", self.show_suppliers),
            ("📈", "التقارير", self.show_reports),
            ("👥", "المستخدمين", self.show_users),
            ("⚙️", "الإعدادات", self.show_settings)
        ]
        
        self.sidebar_buttons = {}
        for icon, text, command in modules:
            # التحقق من الصلاحيات
            if text == "المستخدمين" and not self.current_user.has_permission('all'):
                continue
            
            button = ctk.CTkButton(
                self.sidebar,
                text=f"{icon} {text}",
                command=command,
                font=self.arabic_font,
                anchor="w",
                height=40
            )
            button.pack(fill="x", padx=10, pady=5)
            self.sidebar_buttons[text] = button
    
    def create_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(side="left", fill="both", expand=True, padx=5, pady=5)
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة المعلومات"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="لوحة المعلومات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=20)
        
        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.content_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # عدد المنتجات
        products_count = len(Product.get_all())
        products_card = self.create_stat_card(stats_frame, "📦", "إجمالي المنتجات", str(products_count))
        products_card.pack(side="right", padx=10, pady=10)
        
        # المنتجات منخفضة المخزون
        low_stock = len(Product.get_low_stock_products())
        low_stock_card = self.create_stat_card(stats_frame, "⚠️", "منتجات منخفضة المخزون", str(low_stock))
        low_stock_card.pack(side="right", padx=10, pady=10)
        
        # آخر المعاملات
        recent_frame = ctk.CTkFrame(self.content_frame)
        recent_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        recent_title = ctk.CTkLabel(
            recent_frame,
            text="آخر المعاملات",
            font=("Arial", 16, "bold")
        )
        recent_title.pack(pady=10)
        
        # جدول المعاملات الأخيرة
        self.create_recent_transactions_table(recent_frame)
    
    def create_stat_card(self, parent, icon, title, value):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, width=150, height=100)
        card.pack_propagate(False)
        
        icon_label = ctk.CTkLabel(card, text=icon, font=("Arial", 24))
        icon_label.pack(pady=(10, 5))
        
        value_label = ctk.CTkLabel(card, text=value, font=("Arial", 18, "bold"))
        value_label.pack()
        
        title_label = ctk.CTkLabel(card, text=title, font=("Arial", 10))
        title_label.pack(pady=(0, 10))
        
        return card
    
    def create_recent_transactions_table(self, parent):
        """إنشاء جدول المعاملات الأخيرة"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview
        columns = ("التاريخ", "المنتج", "النوع", "الكمية", "المستخدم")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor="center")
        
        # إضافة البيانات
        recent_transactions = InventoryTransaction.get_all(limit=10)
        for transaction in recent_transactions:
            transaction_type_ar = {
                'in': 'وارد',
                'out': 'صادر',
                'adjustment': 'تعديل'
            }.get(transaction.transaction_type, transaction.transaction_type)
            
            tree.insert("", "end", values=(
                transaction.transaction_date.strftime("%Y-%m-%d %H:%M") if transaction.transaction_date else "",
                transaction.product_name or "",
                transaction_type_ar,
                transaction.quantity,
                transaction.user_name or ""
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
    
    def show_products(self):
        """عرض واجهة إدارة المنتجات"""
        self.clear_content()

        # استيراد واجهة المنتجات
        from views.products_view import ProductsView

        # إنشاء واجهة المنتجات
        self.products_view = ProductsView(self.content_frame, self)
    
    def show_inventory(self):
        """عرض واجهة المخزون"""
        self.clear_content()

        # استيراد واجهة المخزون
        from views.inventory_view import InventoryView

        # إنشاء واجهة المخزون
        self.inventory_view = InventoryView(self.content_frame, self)
    
    def show_incoming(self):
        """عرض واجهة الواردات"""
        self.clear_content()
        
        title = ctk.CTkLabel(
            self.content_frame,
            text="إدارة الواردات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=20)
        
        placeholder = ctk.CTkLabel(
            self.content_frame,
            text="واجهة إدارة الواردات قيد التطوير...",
            font=self.arabic_font
        )
        placeholder.pack(pady=50)
    
    def show_outgoing(self):
        """عرض واجهة الصادرات"""
        self.clear_content()
        
        title = ctk.CTkLabel(
            self.content_frame,
            text="إدارة الصادرات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=20)
        
        placeholder = ctk.CTkLabel(
            self.content_frame,
            text="واجهة إدارة الصادرات قيد التطوير...",
            font=self.arabic_font
        )
        placeholder.pack(pady=50)
    
    def show_transactions(self):
        """عرض واجهة حركات المخزون"""
        self.clear_content()

        # استيراد واجهة حركات المخزون
        from views.transactions_view import TransactionsView

        # إنشاء واجهة حركات المخزون
        self.transactions_view = TransactionsView(self.content_frame, self)
    
    def show_suppliers(self):
        """عرض واجهة الموردين"""
        self.clear_content()

        # استيراد واجهة الموردين
        from views.suppliers_view import SuppliersView

        # إنشاء واجهة الموردين
        self.suppliers_view = SuppliersView(self.content_frame, self)
    
    def show_reports(self):
        """عرض واجهة التقارير"""
        self.clear_content()

        # استيراد واجهة التقارير
        from views.reports_view import ReportsView

        # إنشاء واجهة التقارير
        self.reports_view = ReportsView(self.content_frame, self)
    
    def show_users(self):
        """عرض واجهة المستخدمين"""
        if not self.current_user.has_permission('all'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول إلى هذه الصفحة")
            return

        self.clear_content()

        # استيراد واجهة المستخدمين
        from views.users_view import UsersView

        # إنشاء واجهة المستخدمين
        self.users_view = UsersView(self.content_frame, self)
    
    def show_settings(self):
        """عرض واجهة الإعدادات"""
        self.clear_content()

        # استيراد واجهة الإعدادات
        from views.settings_view import SettingsView

        # إنشاء واجهة الإعدادات
        self.settings_view = SettingsView(self.content_frame, self)
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.create_login_frame()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
