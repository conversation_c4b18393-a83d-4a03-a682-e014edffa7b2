import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="data/warehouse.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """إنشاء مجلد البيانات إذا لم يكن موجوداً"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'employee',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تصنيفات المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                barcode TEXT UNIQUE,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                unit TEXT NOT NULL,
                cost_price REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                min_quantity INTEGER DEFAULT 0,
                max_quantity INTEGER DEFAULT 1000,
                current_quantity INTEGER DEFAULT 0,
                image_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول حركات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                quantity INTEGER NOT NULL,
                unit_price REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                reference_number TEXT,
                supplier_id INTEGER,
                user_id INTEGER,
                notes TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول أوامر التوريد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                supplier_id INTEGER NOT NULL,
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expected_date DATE,
                status TEXT DEFAULT 'pending', -- 'pending', 'received', 'cancelled'
                total_amount REAL DEFAULT 0,
                notes TEXT,
                user_id INTEGER,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول تفاصيل أوامر التوريد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                received_quantity INTEGER DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES purchase_orders (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT
            )
        ''')
        
        # إدراج البيانات الافتراضية
        self.insert_default_data(cursor)
        
        conn.commit()
        conn.close()
    
    def insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        # إنشاء مستخدم افتراضي (admin)
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, full_name, role)
            VALUES ('admin', 'admin123', 'مدير النظام', 'admin')
        ''')
        
        # إنشاء تصنيفات افتراضية
        default_categories = [
            ('أثاث', 'أثاث مكتبي ومنزلي'),
            ('خامات', 'خامات ومواد أولية'),
            ('ملحقات', 'ملحقات وقطع غيار'),
            ('أدوات', 'أدوات ومعدات')
        ]
        
        for name, desc in default_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (name, description)
                VALUES (?, ?)
            ''', (name, desc))
        
        # إعدادات افتراضية
        default_settings = [
            ('company_name', 'شركة إدارة المخازن', 'اسم الشركة'),
            ('currency', 'ريال', 'العملة المستخدمة'),
            ('language', 'ar', 'لغة النظام'),
            ('low_stock_alert', '10', 'تنبيه نقص المخزون'),
            ('backup_interval', '7', 'فترة النسخ الاحتياطي بالأيام')
        ]
        
        for key, value, desc in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO settings (key, value, description)
                VALUES (?, ?, ?)
            ''', (key, value, desc))

# إنشاء مثيل واحد من مدير قاعدة البيانات
db_manager = DatabaseManager()
